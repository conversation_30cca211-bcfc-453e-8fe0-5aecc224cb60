{% extends "emails/base.html" %}

{% block title %}Your {{ digest_type|title }} Digest - ACE Social{% endblock %}

{% block header_title %}Your {{ digest_type|title }} Digest{% endblock %}

{% block content %}
<h2>Hello {{ user_name }},</h2>

<p>Here's a summary of your recent notifications from ACE Social.</p>

<div class="info-box">
    <p><strong>Digest Period:</strong> {{ start_date }} to {{ end_date }}</p>
</div>

{% if notifications %}
{% for category, items in notifications.items %}
<div class="divider"></div>
<h3>{{ category|title }} ({{ items|length }})</h3>

<ul class="notification-list">
    {% for item in items %}
    <li>
        <div class="notification-item">
            <p><strong>{{ item.title }}</strong></p>
            <p>{{ item.message }}</p>
            {% if item.action_url %}
            <a href="{{ item.action_url }}" class="notification-link">View Details</a>
            {% endif %}
            <p class="notification-time">{{ item.timestamp }}</p>
        </div>
    </li>
    {% endfor %}
</ul>
{% endfor %}
{% else %}
<div class="info-box">
    <p>You don't have any new notifications for this period.</p>
</div>
{% endif %}

<div class="divider"></div>

<h3>Manage Your Notifications</h3>
<p>You can customize your notification preferences at any time in your account settings.</p>

<div class="button-container">
    <a href="{{ frontend_url }}/settings/notifications" class="button">Notification Settings</a>
</div>

<div class="info-box">
    <p><strong>💡 Pro Tip:</strong> You can switch between immediate notifications, daily digests, or weekly digests
        based on your preferences.</p>
</div>
{% endblock %}

{% block extra_styles %}
<style>
    .notification-list {
        list-style-type: none;
        padding: 0;
        margin: 20px 0;
    }

    .notification-item {
        background-color: rgba(247, 249, 252, 0.7);
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
        border: 1px solid rgba(229, 232, 237, 0.5);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .notification-item p {
        margin: 8px 0;
    }

    .notification-link {
        display: inline-block;
        margin-top: 8px;
        font-weight: 500;
    }

    .notification-time {
        font-size: 12px;
        color: #888;
        margin-top: 8px;
    }

    @media (prefers-color-scheme: dark) {
        .notification-item {
            background-color: rgba(37, 37, 37, 0.7);
            border: 1px solid rgba(51, 51, 51, 0.5);
        }

        .notification-time {
            color: #aaa;
        }
    }

    @media only screen and (max-width: 640px) {
        .notification-item {
            padding: 12px;
        }
    }
</style>
{% endblock %}