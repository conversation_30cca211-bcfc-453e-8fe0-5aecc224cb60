from setuptools import setup, find_packages

setup(
    name="ace-social",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "fastapi>=0.95.0",
        "uvicorn>=0.21.1",
        "pydantic>=1.10.7",
        "motor>=3.1.2",
        "pymongo>=4.3.3",
        "python-jose>=3.3.0",
        "passlib>=1.7.4",
        "python-multipart>=0.0.6",
        "bcrypt>=4.0.1",
        "httpx>=0.24.0",
        "openai>=0.27.0",
        "python-dotenv>=1.0.0",
        "pytest>=7.3.1",
        "pytest-asyncio>=0.21.0",
        "tenacity>=8.2.2",
        "email-validator>=2.0.0",
        "pytz>=2023.3",
        "pillow>=9.5.0",
        "sendgrid>=6.10.0",
        "jinja2>=3.1.2",
        "premailer>=3.10.0",
        "pywebpush>=1.14.0",
    ],
)
