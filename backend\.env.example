# Environment
ENVIRONMENT=development  # Options: development, staging, production

# MongoDB
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB_NAME=ace_social
MONGODB_MAX_POOL_SIZE=100
MONGODB_MIN_POOL_SIZE=10
MONGODB_MAX_IDLE_TIME_MS=60000
MONGODB_CONNECT_TIMEOUT_MS=5000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=10000
MONGODB_SOCKET_TIMEOUT_MS=30000
MONGODB_RETRY_WRITES=true
MONGODB_W_CONCERN=majority

# JWT
SECRET_KEY=development-secret-key-change-in-production-32-chars-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# OpenAI
OPENAI_API_KEY=your-openai-api-key

# Lemon Squeezy
LEMON_SQUEEZY_API_KEY=your-lemon-squeezy-api-key
LEMON_SQUEEZY_STORE_ID=your-lemon-squeezy-store-id
LEMON_SQUEEZY_WEBHOOK_SECRET=your-lemon-squeezy-webhook-secret
LEMON_SQUEEZY_API_URL=https://api.lemonsqueezy.com/v1

# Social Media API Keys
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
INSTAGRAM_APP_ID=your-instagram-app-id
INSTAGRAM_APP_SECRET=your-instagram-app-secret
LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
PINTEREST_APP_ID=your-pinterest-app-id
PINTEREST_APP_SECRET=your-pinterest-app-secret
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret
THREADS_API_KEY=your-threads-api-key
THREADS_API_SECRET=your-threads-api-secret

# Email
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=your-smtp-host
SMTP_USER=your-smtp-user
SMTP_PASSWORD=your-smtp-password
EMAILS_FROM_EMAIL=your-from-email
EMAILS_FROM_NAME=Your Name

# SendGrid
SENDGRID_API_KEY=your-sendgrid-api-key

# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000

# Web Push Notifications
VAPID_PRIVATE_KEY=your-vapid-private-key
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_CLAIMS_EMAIL=your-vapid-claims-email

# Subscription Plans
FREE_PLAN_ID=free
BASIC_PLAN_ID=basic
PRO_PLAN_ID=pro
ENTERPRISE_PLAN_ID=enterprise

# URL Shortener
URL_SHORTENER_DOMAIN=https://b2b.link
IPINFO_API_KEY=your-ipinfo-api-key

# Redis Configuration (for distributed WebSockets)
REDIS_ENABLED=false
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_MAX_CONNECTIONS=10
REDIS_SOCKET_TIMEOUT=5

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_DEFAULT_LIMIT=100
RATE_LIMIT_DEFAULT_PERIOD=3600

# Observability
LOG_LEVEL=INFO
ENABLE_METRICS=false
METRICS_ENDPOINT=

# Instance identification (for distributed systems)
INSTANCE_ID=instance-1

# File uploads
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=104857600
