<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% block title %}ACE Social{% endblock %}</title>
    <!--[if mso]>
    <style type="text/css">
        table {border-collapse: collapse; border-spacing: 0; margin: 0;}
        div, td {padding: 0;}
        div {margin: 0 !important;}
    </style>
    <noscript>
    <xml>
        <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Base styles */
        :root {
            color-scheme: light dark;
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #1A1A2E;
            margin: 0;
            padding: 0;
            background-color: #F0F4FF;
            background-image:
                radial-gradient(circle at 20% 30%, rgba(108, 75, 250, 0.15) 0%, transparent 25%),
                radial-gradient(circle at 80% 70%, rgba(138, 114, 255, 0.1) 0%, transparent 20%);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 640px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow:
                0 4px 24px rgba(0, 0, 0, 0.08),
                0 1px 3px rgba(108, 75, 250, 0.1),
                0 1px 2px rgba(138, 114, 255, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.7);
        }

        .header {
            text-align: center;
            padding: 35px 0;
            background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 50%, #B19FFF 100%);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDIwIDAgTCAwIDAgTCAwIDIwIiBmaWxsPSJub25lIiBzdHJva2U9InJnYmEoMjU1LDI1NSwyNTUsMC4xKSIgc3Ryb2tlLXdpZHRoPSIxIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2dyaWQpIiAvPjwvc3ZnPg==');
            opacity: 0.2;
            z-index: 0;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.3),
                rgba(255, 255, 255, 0.8),
                rgba(255, 255, 255, 0.3));
            z-index: 2;
        }

        .logo {
            max-width: 180px;
            height: auto;
            position: relative;
            z-index: 1;
            margin-bottom: 15px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            transition: transform 0.3s ease;
        }

        .header h1 {
            color: #ffffff;
            margin: 0;
            font-weight: 700;
            font-size: 28px;
            letter-spacing: -0.5px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            background: linear-gradient(to right, #ffffff, #f0f0ff);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .content {
            padding: 40px 30px;
            background-color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            position: relative;
            border-bottom-left-radius: 24px;
            border-bottom-right-radius: 24px;
        }

        .content::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #6C4BFA, #B19FFF);
            opacity: 0.7;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background-color: rgba(240, 242, 245, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            font-size: 13px;
            color: #666;
            border-top: 1px solid rgba(229, 232, 237, 0.5);
            border-bottom-left-radius: 24px;
            border-bottom-right-radius: 24px;
        }

        h1, h2, h3 {
            color: #1A1A2E;
            margin-top: 0;
            font-weight: 600;
        }

        h2 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #6C4BFA;
            background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        h3 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #1A1A2E;
            letter-spacing: -0.3px;
        }

        p {
            margin-bottom: 20px;
            color: #4A4A68;
            font-size: 16px;
            line-height: 1.7;
        }

        a {
            color: #6C4BFA;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            position: relative;
        }

        a:hover {
            color: #8A72FF;
        }

        a:not(.button):after {
            content: '';
            position: absolute;
            width: 100%;
            transform: scaleX(0);
            height: 1px;
            bottom: 0;
            left: 0;
            background: linear-gradient(90deg, #6C4BFA, #8A72FF);
            transform-origin: bottom right;
            transition: transform 0.3s ease-out;
        }

        a:not(.button):hover:after {
            transform: scaleX(1);
            transform-origin: bottom left;
        }

        .button {
            display: inline-block;
            padding: 14px 32px;
            background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 100%);
            color: white !important;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow:
                0 4px 12px rgba(108, 75, 250, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            position: relative;
            overflow: hidden;
            z-index: 1;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            letter-spacing: 0.3px;
        }

        .button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            border-radius: 12px 12px 0 0;
            z-index: 2;
        }

        .button::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #8A72FF 0%, #6C4BFA 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow:
                0 6px 20px rgba(108, 75, 250, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.3) inset;
            text-decoration: none;
        }

        .button:hover::after {
            opacity: 1;
        }

        .social-links {
            margin-top: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6C4BFA;
            font-weight: 500;
        }

        .social-icon {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #6C4BFA 0%, #8A72FF 100%);
            border-radius: 50%;
            margin: 0 8px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow:
                0 4px 8px rgba(108, 75, 250, 0.2),
                0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            position: relative;
            overflow: hidden;
        }

        .social-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
            border-radius: 50% 50% 0 0;
        }

        .social-icon:hover {
            transform: translateY(-2px) scale(1.1);
            background: linear-gradient(135deg, #8A72FF 0%, #6C4BFA 100%);
            box-shadow: 0 6px 12px rgba(108, 75, 250, 0.3);
        }

        .social-icon img {
            width: 16px;
            height: 16px;
            vertical-align: middle;
            filter: brightness(0) invert(1);
            position: relative;
            z-index: 1;
        }

        .info-box {
            background-color: rgba(108, 75, 250, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #6C4BFA;
            box-shadow:
                0 4px 16px rgba(108, 75, 250, 0.08),
                0 1px 3px rgba(108, 75, 250, 0.1);
            border: 1px solid rgba(108, 75, 250, 0.1);
            position: relative;
            overflow: hidden;
        }

        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(to bottom, rgba(108, 75, 250, 0.03), rgba(108, 75, 250, 0));
            z-index: 0;
        }

        .warning-box {
            background-color: rgba(255, 152, 0, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #ff9800;
            box-shadow:
                0 4px 16px rgba(255, 152, 0, 0.08),
                0 1px 3px rgba(255, 152, 0, 0.1);
            border: 1px solid rgba(255, 152, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .warning-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(to bottom, rgba(255, 152, 0, 0.03), rgba(255, 152, 0, 0));
            z-index: 0;
        }

        .success-box {
            background-color: rgba(76, 175, 80, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #4caf50;
            box-shadow:
                0 4px 16px rgba(76, 175, 80, 0.08),
                0 1px 3px rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.1);
            position: relative;
            overflow: hidden;
        }

        .success-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(to bottom, rgba(76, 175, 80, 0.03), rgba(76, 175, 80, 0));
            z-index: 0;
        }

        .error-box {
            background-color: rgba(244, 67, 54, 0.05);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #f44336;
            box-shadow:
                0 4px 16px rgba(244, 67, 54, 0.08),
                0 1px 3px rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.1);
            position: relative;
            overflow: hidden;
        }

        .error-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(to bottom, rgba(244, 67, 54, 0.03), rgba(244, 67, 54, 0));
            z-index: 0;
        }

        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(229, 232, 237, 0.7), transparent);
            margin: 30px 0;
            position: relative;
        }

        .divider::after {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40%;
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(108, 75, 250, 0.3), transparent);
        }

        .footer-links a {
            color: #6C4BFA;
            margin: 0 10px;
            font-weight: 500;
            position: relative;
            display: inline-block;
            padding: 2px 0;
        }

        .address {
            font-size: 12px;
            color: #888;
            margin-top: 15px;
            font-style: normal;
        }

        .code-block {
            background-color: rgba(247, 249, 252, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 20px;
            font-family: 'SF Mono', 'Consolas', 'Monaco', monospace;
            word-break: break-all;
            margin: 20px 0;
            border: 1px solid rgba(229, 232, 237, 0.5);
            font-size: 14px;
            color: #4A4A68;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            position: relative;
            overflow: hidden;
        }

        .code-block::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #6C4BFA, #8A72FF);
            opacity: 0.7;
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #121212;
                background-image:
                    radial-gradient(circle at 20% 30%, rgba(108, 75, 250, 0.1) 0%, transparent 25%),
                    radial-gradient(circle at 80% 70%, rgba(138, 114, 255, 0.05) 0%, transparent 20%);
                color: #E0E0E0;
            }

            .container {
                background-color: rgba(30, 30, 30, 0.85);
                box-shadow:
                    0 4px 24px rgba(0, 0, 0, 0.3),
                    0 1px 3px rgba(108, 75, 250, 0.15),
                    0 1px 2px rgba(138, 114, 255, 0.15);
                border: 1px solid rgba(60, 60, 60, 0.7);
            }

            .content {
                background-color: rgba(30, 30, 30, 0.7);
            }

            .footer {
                background-color: rgba(37, 37, 37, 0.7);
                color: #AAAAAA;
                border-top: 1px solid rgba(51, 51, 51, 0.5);
            }

            h1, h2, h3 {
                color: #FFFFFF;
            }

            h2 {
                background: linear-gradient(135deg, #8A72FF 0%, #B19FFF 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            p {
                color: #CCCCCC;
            }

            a:not(.button) {
                color: #8A72FF;
            }

            a:not(.button):hover {
                color: #B19FFF;
            }

            .info-box {
                background-color: rgba(108, 75, 250, 0.1);
                box-shadow:
                    0 4px 16px rgba(0, 0, 0, 0.2),
                    0 1px 3px rgba(108, 75, 250, 0.15);
                border: 1px solid rgba(108, 75, 250, 0.15);
            }

            .warning-box {
                background-color: rgba(255, 152, 0, 0.1);
                box-shadow:
                    0 4px 16px rgba(0, 0, 0, 0.2),
                    0 1px 3px rgba(255, 152, 0, 0.15);
                border: 1px solid rgba(255, 152, 0, 0.15);
            }

            .success-box {
                background-color: rgba(76, 175, 80, 0.1);
                box-shadow:
                    0 4px 16px rgba(0, 0, 0, 0.2),
                    0 1px 3px rgba(76, 175, 80, 0.15);
                border: 1px solid rgba(76, 175, 80, 0.15);
            }

            .error-box {
                background-color: rgba(244, 67, 54, 0.1);
                box-shadow:
                    0 4px 16px rgba(0, 0, 0, 0.2),
                    0 1px 3px rgba(244, 67, 54, 0.15);
                border: 1px solid rgba(244, 67, 54, 0.15);
            }

            .code-block {
                background-color: rgba(37, 37, 37, 0.7);
                border: 1px solid rgba(51, 51, 51, 0.5);
                color: #CCCCCC;
            }

            .divider {
                background: linear-gradient(to right, transparent, rgba(51, 51, 51, 0.7), transparent);
            }

            .divider::after {
                background: linear-gradient(to right, transparent, rgba(138, 114, 255, 0.3), transparent);
            }

            .button {
                box-shadow:
                    0 4px 12px rgba(108, 75, 250, 0.4),
                    0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            }

            .button:hover {
                box-shadow:
                    0 6px 20px rgba(108, 75, 250, 0.5),
                    0 0 0 1px rgba(255, 255, 255, 0.2) inset;
            }
        }

        /* Responsive styles */
        @media only screen and (max-width: 640px) {
            .container {
                width: 100%;
                border-radius: 16px;
                margin: 10px;
                max-width: calc(100% - 20px);
            }

            .content {
                padding: 30px 20px;
            }

            .header {
                padding: 25px 0;
            }

            .header h1 {
                font-size: 24px;
            }

            .button {
                display: block;
                width: 100%;
            }

            .info-box, .warning-box, .success-box, .error-box {
                padding: 20px;
            }

            .social-links {
                flex-wrap: wrap;
            }

            .social-icon {
                margin: 5px;
            }

            .footer-links a {
                margin: 0 5px;
                font-size: 12px;
            }
        }

        @media only screen and (max-width: 400px) {
            .container {
                border-radius: 12px;
            }

            .content {
                padding: 25px 15px;
            }

            h2 {
                font-size: 20px;
            }

            h3 {
                font-size: 18px;
            }

            p {
                font-size: 15px;
            }
        }
    </style>
    {% block extra_styles %}{% endblock %}
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="{{ frontend_url }}/assets/logo.svg" alt="ACE Social" class="logo">
            <h1>{% block header_title %}ACE Social{% endblock %}</h1>
        </div>

        <div class="content">
            {% block content %}{% endblock %}
        </div>

        <div class="footer">
            <p>© {% now 'Y' %} ACE Social. All rights reserved.</p>
            <div class="footer-links">
                <a href="{{ frontend_url }}/privacy-policy">Privacy Policy</a> •
                <a href="{{ frontend_url }}/terms">Terms of Service</a> •
                <a href="{{ frontend_url }}/contact">Contact Us</a>
            </div>
            <div class="social-links">
                <a href="#" class="social-icon" aria-label="Twitter">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNMjQgNC41NTdjLS44ODMuMzkyLTEuODMyLjY1Ni0yLjgyOC43NzUgMS4wMTctLjYwOSAxLjc5OC0xLjU3NCAyLjE2NS0yLjcyNC0uOTUxLjU2NC0yLjAwNS45NzQtMy4xMjcgMS4xOTUtLjg5Ny0uOTU3LTIuMTc4LTEuNTU1LTMuNTk0LTEuNTU1LTMuMTc5IDAtNS41MTUgMi45NjYtNC43OTcgNi4wNDUtNC4wOTEtLjIwNS03LjcxOS0yLjE2NS0xMC4xNDgtNS4xNDQtMS4yOSAyLjIxMy0uNjY5IDUuMTA4IDEuNTIzIDYuNTc0LS44MDYtLjAyNi0xLjU2Ni0uMjQ3LTIuMjI5LS42MTYtLjA1NCAyLjI4MSAxLjU4MSA0LjQxNSAzLjk0OSA0Ljg5LS42OTMuMTg4LTEuNDUyLjIzMi0yLjIyNC4wODQuNjI2IDEuOTU2IDIuNDQ0IDMuMzc5IDQuNiAzLjQxOS0yLjA3IDEuNjIzLTQuNjc4IDIuMzQ4LTcuMjkgMi4wNCAyLjE3OSAxLjM5NyA0Ljc2OCAyLjIxMiA3LjU0OCAyLjIxMiA5LjE0MiAwIDE0LjMwNy03LjcyMSAxMy45OTUtMTQuNjQ2Ljk2Mi0uNjk1IDEuNzk3LTEuNTYyIDIuNDU3LTIuNTQ5eiIvPjwvc3ZnPg==" alt="Twitter">
                </a>
                <a href="#" class="social-icon" aria-label="LinkedIn">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNNC45OCAzLjVjMCAxLjM4MS0xLjExIDIuNS0yLjQ4IDIuNXMtMi40OC0xLjExOS0yLjQ4LTIuNWMwLTEuMzggMS4xMS0yLjUgMi40OC0yLjVzMi40OCAxLjEyIDIuNDggMi41em0uMDIgNC41aC01djE2aDV2LTE2em03Ljk4MiAwaC00Ljk2OHYxNmg0Ljk2OXYtOC4zOTljMC00LjY3IDYuMDI5LTUuMDUyIDYuMDI5IDB2OC4zOTloNC45ODh2LTEwLjEzMWMwLTcuODgtOC45MjItNy41OTMtMTEuMDE4LTMuNzE0di0yLjE1NXoiLz48L3N2Zz4=" alt="LinkedIn">
                </a>
                <a href="#" class="social-icon" aria-label="Facebook">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0Ij48cGF0aCBkPSJNOSA4aC0zdjRoM3Y4aDRWMTJoM2wuNS00aC0zLjV2LTEuNWMwLS45LjMtMS41IDEuNS0xLjVoMnYtNGgtMy41Yy0zIDAtNC41IDEuOC00LjUgNXYyeiIvPjwvc3ZnPg==" alt="Facebook">
                </a>
            </div>
            <p>If you didn't request this email, please ignore it or <a href="{{ frontend_url }}/contact">contact us</a>.</p>
            <p class="address">123 Marketing Street, Social Media City, SM 12345</p>
        </div>
    </div>
</body>
</html>
