version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ace-social-backend
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - MONGODB_URL=mongodb://mongodb:27017/ace_social_dev
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=dev-secret-key-change-in-production
      - CORS_ORIGINS=["http://localhost:3001", "http://localhost:5173"]
    volumes:
      - ./backend:/app/backend
      - ./logs:/app/logs
      - ./uploads:/app/uploads
    depends_on:
      - mongodb
      - redis
    networks:
      - ace-social-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health-minimal"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: aceo-frontend
    ports:
      - "3001:3000"
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_ENVIRONMENT=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - ace-social-network
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: ace-social-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=ace_social_dev
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - ace-social-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7.2-alpine
    container_name: ace-social-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass redis123
    volumes:
      - redis_data:/data
    networks:
      - ace-social-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (disabled for now)
  # nginx:
  #   image: nginx:alpine
  #   container_name: aceo-nginx
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
  #     - ./nginx/ssl:/etc/nginx/ssl:ro
  #   depends_on:
  #     - backend
  #     - frontend
  #   networks:
  #     - ace-social-network
  #   restart: unless-stopped

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  ace-social-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
