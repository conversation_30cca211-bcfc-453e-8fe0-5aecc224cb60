@echo off
REM ACE Social Platform - Standardized Build Script for Windows
REM Version: 2.0.0
REM Usage: scripts\build.bat [dev|staging|production] [--clean] [--no-tests] [--verbose]

setlocal enabledelayedexpansion

REM ================================
REM Configuration & Constants
REM ================================

REM Script metadata
set SCRIPT_VERSION=2.0.0
set SCRIPT_NAME=ACE Social Build Script (Windows)

REM Build configuration
set DEFAULT_ENVIRONMENT=dev
set BUILD_LOG_DIR=logs\build
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set BUILD_TIMESTAMP=%dt:~0,8%_%dt:~8,6%
set BUILD_LOG_FILE=%BUILD_LOG_DIR%\build_%BUILD_TIMESTAMP%.log

REM Parse command line arguments
set ENVIRONMENT=%1
if "%ENVIRONMENT%"=="" set ENVIRONMENT=%DEFAULT_ENVIRONMENT%
set CLEAN_BUILD=false
set SKIP_TESTS=false
set VERBOSE=false

REM Parse additional flags
:parse_args
shift
if "%1"=="--clean" (
    set CLEAN_BUILD=true
    goto parse_args
)
if "%1"=="--no-tests" (
    set SKIP_TESTS=true
    goto parse_args
)
if "%1"=="--verbose" (
    set VERBOSE=true
    goto parse_args
)
if "%1"=="--help" goto show_help
if "%1"=="-h" goto show_help
if "%1"=="" goto start_build

goto parse_args

:show_help
echo %SCRIPT_NAME% v%SCRIPT_VERSION%
echo.
echo USAGE:
echo     scripts\build.bat [ENVIRONMENT] [OPTIONS]
echo.
echo ENVIRONMENTS:
echo     dev         Development build (default)
echo     staging     Staging build with optimizations
echo     production  Production build with full optimizations
echo.
echo OPTIONS:
echo     --clean     Clean all caches and artifacts before building
echo     --no-tests  Skip running tests during build
echo     --verbose   Enable verbose logging
echo     --help, -h  Show this help message
echo.
echo EXAMPLES:
echo     scripts\build.bat                          # Development build
echo     scripts\build.bat production               # Production build
echo     scripts\build.bat staging --clean          # Clean staging build
echo     scripts\build.bat production --no-tests    # Production build without tests
echo.
exit /b 0

:start_build

REM ================================
REM Utility Functions
REM ================================

REM Create necessary directories
if not exist "%BUILD_LOG_DIR%" mkdir "%BUILD_LOG_DIR%"
if not exist "logs\frontend" mkdir "logs\frontend"
if not exist "logs\backend" mkdir "logs\backend"
if not exist "logs\admin" mkdir "logs\admin"
if not exist "logs\docker" mkdir "logs\docker"

REM Initialize log file
echo Build Information: > "%BUILD_LOG_FILE%"
echo ================== >> "%BUILD_LOG_FILE%"
echo Script Version: %SCRIPT_VERSION% >> "%BUILD_LOG_FILE%"
echo Environment: %ENVIRONMENT% >> "%BUILD_LOG_FILE%"
echo Timestamp: %date% %time% >> "%BUILD_LOG_FILE%"
echo Clean Build: %CLEAN_BUILD% >> "%BUILD_LOG_FILE%"
echo Skip Tests: %SKIP_TESTS% >> "%BUILD_LOG_FILE%"
echo Verbose: %VERBOSE% >> "%BUILD_LOG_FILE%"
echo ================== >> "%BUILD_LOG_FILE%"

REM ================================
REM Validation Functions
REM ================================

echo 🔍 Validating prerequisites...

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed or not in PATH
    exit /b 1
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not installed or not in PATH
    exit /b 1
)

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    exit /b 1
)

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not installed or not in PATH
    exit /b 1
)

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker and try again.
    exit /b 1
)

REM Check if required files exist
if not exist "package.json" (
    echo ❌ package.json not found. Please run this script from the project root.
    exit /b 1
)

if not exist "frontend\package.json" (
    echo ❌ frontend\package.json not found.
    exit /b 1
)

if not exist "backend\requirements.txt" (
    echo ❌ backend\requirements.txt not found.
    exit /b 1
)

echo ✅ All prerequisites validated

REM ================================
REM Clean Build Artifacts
REM ================================

if "%CLEAN_BUILD%"=="true" (
    echo 🧹 Cleaning build artifacts...

    if exist "frontend\node_modules\.vite" rmdir /s /q "frontend\node_modules\.vite"
    if exist "frontend\dist" rmdir /s /q "frontend\dist"
    if exist "admin-app\dist" rmdir /s /q "admin-app\dist"

    REM Clean Python cache
    for /d /r backend %%d in (__pycache__) do @if exist "%%d" rmdir /s /q "%%d"
    for /d /r backend %%d in (*.egg-info) do @if exist "%%d" rmdir /s /q "%%d"

    echo ✅ Build artifacts cleaned
)

REM ================================
REM Build Functions
REM ================================

echo 📦 Building Frontend Application...
echo [1/4] Frontend Build

cd frontend

REM Install dependencies if needed or if clean build
if not exist "node_modules" (
    echo 📥 Installing frontend dependencies...
    if "%VERBOSE%"=="true" (
        npm ci
    ) else (
        npm ci > "..\logs\frontend\npm-install.log" 2>&1
    )
    echo ✅ Frontend dependencies installed
) else if "%CLEAN_BUILD%"=="true" (
    echo 📥 Reinstalling frontend dependencies...
    if "%VERBOSE%"=="true" (
        npm ci
    ) else (
        npm ci > "..\logs\frontend\npm-install.log" 2>&1
    )
    echo ✅ Frontend dependencies installed
)

REM Run linting if not production
if not "%ENVIRONMENT%"=="production" (
    echo 🔍 Running frontend linting...
    npm run lint > "..\logs\frontend\lint.log" 2>&1
    if errorlevel 1 (
        echo ⚠️ Frontend linting failed. Check logs\frontend\lint.log
    ) else (
        echo ✅ Frontend linting passed
    )
)

REM Type checking
echo 🔍 Running TypeScript type checking...
npm run type-check > "..\logs\frontend\type-check.log" 2>&1
if errorlevel 1 (
    echo ⚠️ TypeScript type checking failed. Check logs\frontend\type-check.log
) else (
    echo ✅ TypeScript type checking passed
)

REM Run build based on environment
echo 🔨 Building frontend for %ENVIRONMENT% environment...
if "%ENVIRONMENT%"=="production" (
    if "%VERBOSE%"=="true" (
        npm run build:production
    ) else (
        npm run build:production > "..\logs\frontend\build.log" 2>&1
    )
) else if "%ENVIRONMENT%"=="staging" (
    set NODE_ENV=staging
    if "%VERBOSE%"=="true" (
        npm run build
    ) else (
        npm run build > "..\logs\frontend\build.log" 2>&1
    )
) else (
    if "%VERBOSE%"=="true" (
        npm run build
    ) else (
        npm run build > "..\logs\frontend\build.log" 2>&1
    )
)

if errorlevel 1 (
    echo ❌ Frontend build failed
    cd ..
    exit /b 1
)

REM Check build output
if exist "dist" (
    for /f %%i in ('dir /s /-c "dist" ^| find "bytes"') do set "frontend_size=%%i"
    echo ✅ Frontend build completed
) else (
    echo ❌ Frontend build failed - no output generated
    cd ..
    exit /b 1
)

cd ..

echo 🐍 Building Backend Application...
echo [2/4] Backend Build

cd backend

REM Check if virtual environment exists or create one
if not exist "..\venv" (
    echo 📦 Creating Python virtual environment...
    cd ..
    python -m venv venv
    cd backend
    echo ✅ Virtual environment created
) else if "%CLEAN_BUILD%"=="true" (
    echo 📦 Recreating Python virtual environment...
    cd ..
    rmdir /s /q venv
    python -m venv venv
    cd backend
    echo ✅ Virtual environment recreated
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call ..\venv\Scripts\activate.bat

REM Upgrade pip
echo 📈 Upgrading pip...
if "%VERBOSE%"=="true" (
    python -m pip install --upgrade pip
) else (
    python -m pip install --upgrade pip > "..\logs\backend\pip-upgrade.log" 2>&1
)

REM Install dependencies
echo 📥 Installing backend dependencies...
if "%VERBOSE%"=="true" (
    pip install -r requirements.txt
) else (
    pip install -r requirements.txt > "..\logs\backend\pip-install.log" 2>&1
)

REM Install package in development mode
echo 📦 Installing package in development mode...
if "%VERBOSE%"=="true" (
    pip install -e .
) else (
    pip install -e . > "..\logs\backend\pip-install-dev.log" 2>&1
)

REM Run linting if not production
if not "%ENVIRONMENT%"=="production" (
    echo 🔍 Running backend linting...
    flake8 . > "..\logs\backend\lint.log" 2>&1
    if errorlevel 1 (
        echo ⚠️ Backend linting failed. Check logs\backend\lint.log
    ) else (
        echo ✅ Backend linting passed
    )
)

REM Generate requirements lock file for production
if "%ENVIRONMENT%"=="production" (
    echo 🔒 Generating requirements lock file...
    pip freeze > requirements.lock
    echo ✅ Requirements lock file generated
)

echo ✅ Backend build completed
cd ..

echo 🎛️ Building Admin Application...
echo [3/4] Admin Build

cd admin-app

REM Install dependencies if needed or if clean build
if not exist "node_modules" (
    echo 📥 Installing admin dependencies...
    if "%VERBOSE%"=="true" (
        npm ci
    ) else (
        npm ci > "..\logs\admin\npm-install.log" 2>&1
    )
    echo ✅ Admin dependencies installed
) else if "%CLEAN_BUILD%"=="true" (
    echo 📥 Reinstalling admin dependencies...
    if "%VERBOSE%"=="true" (
        npm ci
    ) else (
        npm ci > "..\logs\admin\npm-install.log" 2>&1
    )
    echo ✅ Admin dependencies installed
)

REM Run linting if not production
if not "%ENVIRONMENT%"=="production" (
    echo 🔍 Running admin linting...
    npm run lint > "..\logs\admin\lint.log" 2>&1
    if errorlevel 1 (
        echo ⚠️ Admin linting failed. Check logs\admin\lint.log
    ) else (
        echo ✅ Admin linting passed
    )
)

REM Run build based on environment
echo 🔨 Building admin for %ENVIRONMENT% environment...
if "%ENVIRONMENT%"=="production" (
    if "%VERBOSE%"=="true" (
        npm run build:production
    ) else (
        npm run build:production > "..\logs\admin\build.log" 2>&1
    )
) else (
    if "%VERBOSE%"=="true" (
        npm run build
    ) else (
        npm run build > "..\logs\admin\build.log" 2>&1
    )
)

if errorlevel 1 (
    echo ❌ Admin build failed
    cd ..
    exit /b 1
)

REM Check build output
if exist "dist" (
    echo ✅ Admin build completed
) else (
    echo ❌ Admin build failed - no output generated
    cd ..
    exit /b 1
)

cd ..

echo 🐳 Building Docker Images...
echo [4/4] Docker Build

REM Set Docker build arguments
set docker_args=
if "%CLEAN_BUILD%"=="true" set docker_args=--no-cache

REM Enable BuildKit for better performance
set DOCKER_BUILDKIT=1

echo 🔨 Building Docker images for %ENVIRONMENT% environment...

if "%ENVIRONMENT%"=="production" (
    if "%VERBOSE%"=="true" (
        docker-compose -f docker-compose.prod.yml build %docker_args%
    ) else (
        docker-compose -f docker-compose.prod.yml build %docker_args% > "logs\docker\build.log" 2>&1
    )
) else if "%ENVIRONMENT%"=="staging" (
    if "%VERBOSE%"=="true" (
        docker-compose -f docker-compose.staging.yml build %docker_args%
    ) else (
        docker-compose -f docker-compose.staging.yml build %docker_args% > "logs\docker\build.log" 2>&1
    )
) else (
    if "%VERBOSE%"=="true" (
        docker-compose -f docker-compose.dev.yml build %docker_args%
    ) else (
        docker-compose -f docker-compose.dev.yml build %docker_args% > "logs\docker\build.log" 2>&1
    )
)

if errorlevel 1 (
    echo ❌ Docker build failed
    exit /b 1
)

REM Verify images were built
echo 🔍 Verifying Docker images...
docker images | findstr /i "ace-social aceo" > nul
if errorlevel 1 (
    echo ❌ No Docker images were built
    exit /b 1
) else (
    echo ✅ Docker images built successfully
    echo 📊 Docker image sizes:
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | findstr /i "ace-social aceo"
)

REM ================================
REM Testing
REM ================================

if "%SKIP_TESTS%"=="true" (
    echo ℹ️ Skipping tests (--no-tests flag provided)
    goto create_env
)

if not "%ENVIRONMENT%"=="production" (
    echo 🧪 Running Test Suite...

    set tests_passed=0
    set tests_failed=0

    REM Frontend tests
    echo 🔍 Running frontend tests...
    cd frontend
    if "%VERBOSE%"=="true" (
        npm run test
        if errorlevel 1 (
            echo ⚠️ Frontend tests failed
            set /a tests_failed+=1
        ) else (
            echo ✅ Frontend tests passed
            set /a tests_passed+=1
        )
    ) else (
        npm run test > "..\logs\frontend\tests.log" 2>&1
        if errorlevel 1 (
            echo ⚠️ Frontend tests failed. Check logs\frontend\tests.log
            set /a tests_failed+=1
        ) else (
            echo ✅ Frontend tests passed
            set /a tests_passed+=1
        )
    )
    cd ..

    REM Backend tests
    echo 🔍 Running backend tests...
    cd backend
    call ..\venv\Scripts\activate.bat
    if "%VERBOSE%"=="true" (
        python -m pytest -v
        if errorlevel 1 (
            echo ⚠️ Backend tests failed
            set /a tests_failed+=1
        ) else (
            echo ✅ Backend tests passed
            set /a tests_passed+=1
        )
    ) else (
        python -m pytest > "..\logs\backend\tests.log" 2>&1
        if errorlevel 1 (
            echo ⚠️ Backend tests failed. Check logs\backend\tests.log
            set /a tests_failed+=1
        ) else (
            echo ✅ Backend tests passed
            set /a tests_passed+=1
        )
    )
    cd ..

    REM Admin tests
    echo 🔍 Running admin tests...
    cd admin-app
    if "%VERBOSE%"=="true" (
        npm run test
        if errorlevel 1 (
            echo ⚠️ Admin tests failed
            set /a tests_failed+=1
        ) else (
            echo ✅ Admin tests passed
            set /a tests_passed+=1
        )
    ) else (
        npm run test > "..\logs\admin\tests.log" 2>&1
        if errorlevel 1 (
            echo ⚠️ Admin tests failed. Check logs\admin\tests.log
            set /a tests_failed+=1
        ) else (
            echo ✅ Admin tests passed
            set /a tests_passed+=1
        )
    )
    cd ..

    echo ℹ️ Test Summary: %tests_passed% passed, %tests_failed% failed

    if %tests_failed% gtr 0 if "%ENVIRONMENT%"=="production" (
        echo ❌ Tests failed in production build. Aborting.
        exit /b 1
    )
)

:create_env

REM ================================
REM Environment Configuration
REM ================================

echo 📝 Creating environment configuration...

set ENV_FILE=.env.%ENVIRONMENT%

if not exist "%ENV_FILE%" (
    echo 🔧 Generating %ENV_FILE%...

    REM Generate secure random keys (simplified for Windows)
    set SECRET_KEY=change_this_secret_key_in_production_%RANDOM%%RANDOM%
    set MONGO_PASSWORD=secure_mongo_password_%RANDOM%
    set REDIS_PASSWORD=secure_redis_password_%RANDOM%

    (
        echo # ACE Social Platform - Environment Configuration
        echo # Environment: %ENVIRONMENT%
        echo # Generated: %date% %time%
        echo # WARNING: Update API keys and passwords before deployment
        echo.
        echo # Application Settings
        echo ENVIRONMENT=%ENVIRONMENT%
        if "%ENVIRONMENT%"=="production" (
            echo DEBUG=false
        ) else (
            echo DEBUG=true
        )
        echo SECRET_KEY=%SECRET_KEY%
        echo.
        echo # Database Configuration
        echo MONGODB_URL=mongodb://admin:%MONGO_PASSWORD%@localhost:27017/ace_social_%ENVIRONMENT%?authSource=admin
        echo REDIS_URL=redis://:%REDIS_PASSWORD%@localhost:6379/0
        echo.
        echo # Database Credentials
        echo MONGO_ROOT_USERNAME=admin
        echo MONGO_ROOT_PASSWORD=%MONGO_PASSWORD%
        echo REDIS_PASSWORD=%REDIS_PASSWORD%
        echo.
        echo # API Keys ^(REQUIRED - Update these values^)
        echo OPENAI_API_KEY=your_openai_api_key_here
        echo SENDGRID_API_KEY=your_sendgrid_api_key_here
        echo LEMON_SQUEEZY_API_KEY=your_lemon_squeezy_api_key_here
        echo LEMON_SQUEEZY_WEBHOOK_SECRET=your_webhook_secret_here
        echo.
        echo # CORS Configuration
        echo CORS_ORIGINS=["http://localhost:3000","http://localhost:3001","http://localhost:5173"]
        echo.
        echo # Performance Settings
        echo MAX_UPLOAD_SIZE=10485760
        echo RATE_LIMIT_PER_MINUTE=100
        echo.
        echo # Security Settings
        echo SESSION_TIMEOUT=3600
        echo PASSWORD_MIN_LENGTH=8
        echo ENABLE_2FA=false
        echo.
        echo # Logging
        if "%ENVIRONMENT%"=="production" (
            echo LOG_LEVEL=INFO
        ) else (
            echo LOG_LEVEL=DEBUG
        )
        echo LOG_FORMAT=json
        echo.
        echo # Feature Flags
        echo ENABLE_ANALYTICS=true
        echo ENABLE_CACHING=true
        echo ENABLE_COMPRESSION=true
    ) > "%ENV_FILE%"

    echo ✅ Environment file created: %ENV_FILE%
    echo ⚠️ IMPORTANT: Update API keys and review security settings in %ENV_FILE%

) else (
    echo ✅ Environment file already exists: %ENV_FILE%

    REM Check for placeholder API keys
    findstr /c:"your_.*_api_key_here" "%ENV_FILE%" >nul
    if not errorlevel 1 (
        echo ⚠️ Environment file contains placeholder API keys. Please update them.
    )
)

REM ================================
REM Build Validation
REM ================================

echo 🔍 Validating Build Output...

set validation_errors=0

REM Check frontend build
if exist "frontend\dist" (
    for /f %%i in ('dir /s /-c "frontend\dist" ^| find "bytes"') do set "frontend_size=%%i"
    echo ✅ Frontend build output validated

    REM Check for critical files
    if not exist "frontend\dist\index.html" (
        echo ❌ Missing critical frontend file: index.html
        set /a validation_errors+=1
    )
    if not exist "frontend\dist\assets" (
        echo ❌ Missing critical frontend directory: assets
        set /a validation_errors+=1
    )
) else (
    echo ❌ Frontend build output missing or empty
    set /a validation_errors+=1
)

REM Check admin build
if exist "admin-app\dist" (
    echo ✅ Admin build output validated
) else (
    echo ❌ Admin build output missing or empty
    set /a validation_errors+=1
)

REM Check backend installation
cd backend
call ..\venv\Scripts\activate.bat
python -c "import app; print('Backend import successful')" >nul 2>&1
if errorlevel 1 (
    echo ❌ Backend installation validation failed
    set /a validation_errors+=1
) else (
    echo ✅ Backend installation validated
)
cd ..

REM Check Docker images
docker images | findstr /i "ace-social" >nul
if errorlevel 1 (
    echo ⚠️ No Docker images found (may not have been built)
) else (
    echo ✅ Docker images validated
)

if %validation_errors% equ 0 (
    echo ✅ All build outputs validated successfully
) else (
    echo ❌ Build validation failed with %validation_errors% errors
    exit /b 1
)

REM ================================
REM Build Report
REM ================================

echo 📊 Generating build report...

set REPORT_FILE=%BUILD_LOG_DIR%\build_report_%BUILD_TIMESTAMP%.json

(
    echo {
    echo   "build": {
    echo     "version": "%SCRIPT_VERSION%",
    echo     "environment": "%ENVIRONMENT%",
    echo     "timestamp": "%date% %time%",
    echo     "clean_build": %CLEAN_BUILD%,
    echo     "skip_tests": %SKIP_TESTS%,
    echo     "verbose": %VERBOSE%
    echo   },
    echo   "system": {
    echo     "os": "Windows",
    echo     "node_version": "$(node --version 2>nul || echo N/A)",
    echo     "python_version": "$(python --version 2>nul || echo N/A)",
    echo     "docker_version": "$(docker --version 2>nul || echo N/A)"
    echo   }
    echo }
) > "%REPORT_FILE%"

echo ✅ Build report generated: %REPORT_FILE%

REM ================================
REM Build Completion
REM ================================

echo.
echo ================================
echo 🎉 Build Completed Successfully!
echo ================================

echo ℹ️ Next Steps:
if "%ENVIRONMENT%"=="production" (
    echo   1. Review and update API keys in .env.production
    echo   2. Run security audit: npm run security:audit
    echo   3. Deploy: npm run start:prod
    echo   4. Monitor: npm run logs
) else if "%ENVIRONMENT%"=="staging" (
    echo   1. Update environment variables in .env.staging
    echo   2. Deploy: npm run deploy:staging
    echo   3. Run integration tests
) else (
    echo   1. Start development: npm run dev
    echo   2. Or with Docker: npm run start:dev
    echo   3. View logs: npm run logs
)

echo.
echo ℹ️ Build logs available in: %BUILD_LOG_FILE%
echo ℹ️ Environment: %ENVIRONMENT%
echo ℹ️ Build ID: %BUILD_TIMESTAMP%

endlocal
exit /b 0
