# ACE Social Platform - Multi-stage Docker Build
# Version: 2.0.0
# Production-ready configuration with security and optimization

# Build arguments
ARG NODE_VERSION=18
ARG PYTHON_VERSION=3.11
ARG BUILD_DATE
ARG BUILD_VERSION
ARG BUILD_REVISION

# ================================
# Stage 1: Base Node Image
# ================================
FROM node:${NODE_VERSION}-alpine AS node-base

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# ================================
# Stage 2: Frontend Dependencies
# ================================
FROM node-base AS frontend-deps

# Copy package files for better caching
COPY frontend/package*.json ./frontend/
COPY admin-app/package*.json ./admin-app/

# Install dependencies with optimizations (including dev dependencies for build)
RUN cd frontend && \
    npm ci --silent && \
    npm cache clean --force

RUN cd admin-app && \
    npm ci --silent && \
    npm cache clean --force

# ================================
# Stage 3: Frontend Build
# ================================
FROM node-base AS frontend-builder

# Copy dependencies from previous stage
COPY --from=frontend-deps /app/frontend/node_modules ./frontend/node_modules
COPY --from=frontend-deps /app/admin-app/node_modules ./admin-app/node_modules

# Copy source code
COPY frontend/ ./frontend/
COPY admin-app/ ./admin-app/

# Build frontend applications
WORKDIR /app/frontend
RUN npm run build:no-lint

WORKDIR /app/admin-app
RUN npm run build

# Optimize build output
RUN find dist -name "*.map" -delete && \
    find dist -name "*.txt" -delete

# ================================
# Stage 4: Python Base
# ================================
FROM python:${PYTHON_VERSION}-slim AS python-base

# Set environment variables for Python
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app/backend

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# ================================
# Stage 5: Backend Dependencies
# ================================
FROM python-base AS backend-deps

# Set working directory
WORKDIR /app/backend

# Copy requirements first for better caching
COPY backend/requirements.txt ./

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# ================================
# Stage 6: Backend Build
# ================================
FROM python-base AS backend-builder

# Set working directory
WORKDIR /app/backend

# Copy requirements and install dependencies
COPY backend/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy backend source
COPY backend/ ./

# Install backend package
RUN pip install -e . --no-deps

# ================================
# Stage 7: Production Image
# ================================
FROM python:${PYTHON_VERSION}-slim AS production

# Set build metadata
LABEL org.opencontainers.image.title="ACE Social Platform"
LABEL org.opencontainers.image.description="Social media management platform with AI capabilities"
LABEL org.opencontainers.image.version="${BUILD_VERSION}"
LABEL org.opencontainers.image.created="${BUILD_DATE}"
LABEL org.opencontainers.image.revision="${BUILD_REVISION}"
LABEL org.opencontainers.image.source="https://github.com/Tayyabjv1/Social-media-Platform"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app/backend \
    PORT=8000 \
    ENVIRONMENT=production \
    WORKERS=1

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser -s /bin/false appuser

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy Python dependencies from builder stage
COPY --from=backend-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=backend-builder /usr/local/bin /usr/local/bin

# Copy backend application
COPY --from=backend-builder /app/backend ./backend/

# Copy built frontend applications
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist/
COPY --from=frontend-builder /app/admin-app/dist ./admin-app/dist/

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/uploads /app/tmp && \
    chown -R appuser:appuser /app && \
    chmod -R 755 /app && \
    chmod -R 777 /app/logs /app/uploads /app/tmp

# Create health check script
RUN echo '#!/bin/sh\ncurl -f http://localhost:$PORT/health-minimal || exit 1' > /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Add startup script for better process management
RUN echo '#!/bin/sh\nset -e\necho "Starting ACE Social Platform..."\ncd /app/backend\nexec /usr/local/bin/uvicorn app.main:app --host 0.0.0.0 --port $PORT --workers $WORKERS' > /usr/local/bin/start.sh && \
    chmod +x /usr/local/bin/start.sh

# Switch to non-root user
USER appuser

# Health check with improved configuration
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Expose port
EXPOSE $PORT

# Use startup script as entrypoint
CMD ["/usr/local/bin/start.sh"]

# ================================
# Stage 8: Development Image
# ================================
FROM production AS development

# Switch back to root for development tools installation
USER root

# Install development dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Install development Python packages
RUN pip install --no-cache-dir \
    pytest \
    pytest-cov \
    black \
    flake8 \
    isort \
    debugpy

# Set development environment
ENV ENVIRONMENT=development \
    DEBUG=true \
    WORKERS=1

# Switch back to appuser
USER appuser

# Override CMD for development with hot reload
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
