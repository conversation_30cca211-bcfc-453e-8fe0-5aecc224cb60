# Email System Transition Guide

This document provides instructions for transitioning to the enhanced email system in ACEO.

## Overview

The enhanced email system includes the following improvements:

1. **New Email Templates**:
   - `payment_failed.html` - For notifying users of failed payment attempts
   - `subscription_cancelled.html` - For confirming subscription cancellations
   - `subscription_renewed.html` - For notifying users of successful subscription renewals
   - `account_deletion.html` - For confirming account deletion requests
   - `rate_limit.html` - For notifying users when they reach usage limits
   - `content_failed.html` - For notifying users when content publishing fails
   - `email_verification_reminder.html` - For follow-up emails to users who haven't verified their email
   - `email_digest.html` - For daily/weekly digest of notifications

2. **Enhanced Email Services**:
   - Email digest functionality
   - Email verification reminder system
   - Improved email notification service
   - Additional email types for various system events

3. **Database Changes**:
   - New `digest_notifications` collection
   - Additional user notification preferences
   - Email verification reminder tracking fields

## Prerequisites

Before proceeding with the transition, ensure that:

1. You have a recent backup of the database
2. You have scheduled a maintenance window for the transition
3. You have tested the enhanced email system in a staging environment
4. You have coordinated with the operations team

## Transition Process

The transition process is automated using the `email_system_transition.py` script. This script:

1. Creates a backup of the original email service files
2. Runs the database migration
3. Replaces the original email service files with the enhanced versions
4. Adds appropriate logging to track the transition

### Running the Transition

To run the transition:

```bash
# Navigate to the backend directory
cd Social-media-Platform-master\backend

# Run the transition script
python -m scripts.email_system_transition
```

The script will:
- Create backups of the original files in `app/services/backup`
- Run the database migration to add necessary fields and collections
- Replace the original files with the enhanced versions
- Verify the transition was successful
- Log all actions to a file named `email_system_transition_YYYYMMDD_HHMMSS.log`

### Monitoring the Transition

During and after the transition:

1. Monitor the application logs for any errors related to the email system
2. Check that emails are being sent correctly
3. Verify that email digests are being generated for users who have enabled them
4. Confirm that email verification reminders are being sent to users who haven't verified their email

## Rollback Procedure

If you encounter issues with the enhanced email system, you can roll back to the original version using the `email_system_rollback.py` script.

To run the rollback:

```bash
# Navigate to the backend directory
cd b2b infulancer tool (1)\b2b infulancer tool\backend

# Run the rollback script
python -m scripts.email_system_rollback
```

The script will:
- Restore the original email service files from the backups
- Verify the rollback was successful
- Log all actions to a file named `email_system_rollback_YYYYMMDD_HHMMSS.log`

**Note**: The rollback script does not revert the database changes made during the transition. If necessary, restore the database from a backup taken before the transition.

## Post-Transition Tasks

After successfully transitioning to the enhanced email system:

1. **Update Documentation**:
   - Update API documentation to reflect the new email functionality
   - Update internal documentation for the operations team

2. **Add Monitoring**:
   - Add monitoring for the new email functionality
   - Set up alerts for any issues with email sending

3. **User Communication**:
   - Inform users about the new email digest functionality
   - Provide instructions for configuring email preferences

4. **Clean Up**:
   - After confirming everything is working correctly, you can remove the backup files
   - Keep the transition and rollback scripts for future reference

## Troubleshooting

### Common Issues

1. **Emails not being sent**:
   - Check the application logs for errors
   - Verify that the email service is properly configured
   - Check that the email templates exist in the correct location

2. **Database migration errors**:
   - Check the migration logs for specific errors
   - Verify that the database connection is working
   - Ensure that the database user has the necessary permissions

3. **Missing email templates**:
   - Verify that all email templates are in the `app/templates/emails` directory
   - Check that the templates have the correct file names

### Getting Help

If you encounter issues that you cannot resolve:

1. Check the detailed logs in the `email_system_transition_YYYYMMDD_HHMMSS.log` file
2. Contact the development team for assistance
3. If necessary, use the rollback script to revert to the original email system
