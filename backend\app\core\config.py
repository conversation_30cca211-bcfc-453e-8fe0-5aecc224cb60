import os
from pathlib import Path
from typing import List, Optional, Union, Annotated, cast
from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Load .env file from the backend directory
env_path = Path(__file__).parent.parent.parent / ".env"
load_dotenv(dotenv_path=env_path)

class Settings(BaseSettings):
    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")  # Options: development, staging, production

    PROJECT_NAME: str = "ACEO"
    API_V1_STR: str = "/api/v1"

    # API Versioning
    API_VERSION: str = "1.0.0"
    API_LATEST_VERSION: str = "1.0.0"
    API_SUPPORTED_VERSIONS: List[str] = ["1.0.0"]
    API_DEPRECATED_VERSIONS: List[str] = []
    API_VERSION_HEADER: str = "X-API-Version"

    # CORS
    CORS_ORIGINS: List[AnyHttpUrl] = []

    @field_validator("CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        # Handle None or empty values
        if v is None or v == "":
            # Only allow localhost origins in development
            environment = os.getenv("ENVIRONMENT", "development")
            if environment == "development":
                return ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"]
            else:
                # Production requires explicit CORS origins
                return []
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(f"Invalid CORS_ORIGINS value: {v}")

    # MongoDB Connection Settings
    MONGODB_URL: str = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    MONGODB_DB_NAME: str = os.getenv("MONGODB_DB_NAME", "ace_social")

    # MongoDB Connection Pool Settings
    MONGODB_MAX_POOL_SIZE: int = int(os.getenv("MONGODB_MAX_POOL_SIZE", "100"))
    MONGODB_MIN_POOL_SIZE: int = int(os.getenv("MONGODB_MIN_POOL_SIZE", "10"))
    MONGODB_MAX_IDLE_TIME_MS: int = int(os.getenv("MONGODB_MAX_IDLE_TIME_MS", "60000"))  # 1 minute
    MONGODB_MAX_CONNECTING: int = int(os.getenv("MONGODB_MAX_CONNECTING", "2"))  # Max simultaneous connection attempts

    # MongoDB Timeout Settings
    MONGODB_CONNECT_TIMEOUT_MS: int = int(os.getenv("MONGODB_CONNECT_TIMEOUT_MS", "2000"))  # 2 seconds
    MONGODB_SERVER_SELECTION_TIMEOUT_MS: int = int(os.getenv("MONGODB_SERVER_SELECTION_TIMEOUT_MS", "3000"))  # 3 seconds
    MONGODB_SOCKET_TIMEOUT_MS: int = int(os.getenv("MONGODB_SOCKET_TIMEOUT_MS", "5000"))  # 5 seconds
    MONGODB_HEARTBEAT_FREQUENCY_MS: int = int(os.getenv("MONGODB_HEARTBEAT_FREQUENCY_MS", "5000"))  # 5 seconds

    # MongoDB Write Concern Settings
    MONGODB_RETRY_WRITES: bool = os.getenv("MONGODB_RETRY_WRITES", "true").lower() == "true"
    MONGODB_W_CONCERN: str = os.getenv("MONGODB_W_CONCERN", "majority")  # Write concern for data durability

    # MongoDB Read Preference Settings
    MONGODB_READ_PREFERENCE: str = os.getenv("MONGODB_READ_PREFERENCE", "primaryPreferred")

    # MongoDB Security Settings
    MONGODB_TLS_ENABLED: bool = os.getenv("MONGODB_TLS_ENABLED", "false").lower() == "true"

    # JWT
    SECRET_KEY: str = os.getenv("SECRET_KEY", "")  # Empty default, will be validated
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "60"))  # 60 minutes
    REFRESH_TOKEN_EXPIRE_DAYS: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))  # 7 days

    # Enhanced JWT Security Settings
    JWT_AUDIENCE: Optional[str] = os.getenv("JWT_AUDIENCE", None)  # Audience claim
    JWT_ISSUER: Optional[str] = os.getenv("JWT_ISSUER", None)  # Issuer claim
    JWT_REQUIRE_FINGERPRINT: bool = os.getenv("JWT_REQUIRE_FINGERPRINT", "false").lower() == "true"  # Require fingerprint
    JWT_LEEWAY_SECONDS: int = int(os.getenv("JWT_LEEWAY_SECONDS", "30"))  # Leeway for clock skew
    JWT_BLACKLIST_ENABLED: bool = os.getenv("JWT_BLACKLIST_ENABLED", "true").lower() == "true"  # Enable token blacklisting
    JWT_BLACKLIST_GRACE_PERIOD: int = int(os.getenv("JWT_BLACKLIST_GRACE_PERIOD", "10"))  # Grace period in seconds

    @field_validator("SECRET_KEY")
    def validate_secret_key(cls, v: str) -> str:
        if not v or len(v) < 32:
            raise ValueError("SECRET_KEY environment variable must be set with a secure key of at least 32 characters")
        return v

    # OpenAI
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")

    # Payment Processing - Lemon Squeezy
    LEMON_SQUEEZY_API_KEY: str = os.getenv("LEMON_SQUEEZY_API_KEY", "")
    LEMON_SQUEEZY_STORE_ID: str = os.getenv("LEMON_SQUEEZY_STORE_ID", "")
    LEMON_SQUEEZY_WEBHOOK_SECRET: str = os.getenv("LEMON_SQUEEZY_WEBHOOK_SECRET", "")
    LEMON_SQUEEZY_API_URL: str = os.getenv("LEMON_SQUEEZY_API_URL", "https://api.lemonsqueezy.com/v1")

    # Social Media API Keys
    FACEBOOK_APP_ID: str = os.getenv("FACEBOOK_APP_ID", "")
    FACEBOOK_APP_SECRET: str = os.getenv("FACEBOOK_APP_SECRET", "")
    TWITTER_API_KEY: str = os.getenv("TWITTER_API_KEY", "")
    TWITTER_API_SECRET: str = os.getenv("TWITTER_API_SECRET", "")
    INSTAGRAM_APP_ID: str = os.getenv("INSTAGRAM_APP_ID", "")
    INSTAGRAM_APP_SECRET: str = os.getenv("INSTAGRAM_APP_SECRET", "")
    LINKEDIN_CLIENT_ID: str = os.getenv("LINKEDIN_CLIENT_ID", "")
    LINKEDIN_CLIENT_SECRET: str = os.getenv("LINKEDIN_CLIENT_SECRET", "")
    PINTEREST_APP_ID: str = os.getenv("PINTEREST_APP_ID", "")
    PINTEREST_APP_SECRET: str = os.getenv("PINTEREST_APP_SECRET", "")
    TIKTOK_CLIENT_KEY: str = os.getenv("TIKTOK_CLIENT_KEY", "")
    TIKTOK_CLIENT_SECRET: str = os.getenv("TIKTOK_CLIENT_SECRET", "")
    THREADS_API_KEY: str = os.getenv("THREADS_API_KEY", "")
    THREADS_API_SECRET: str = os.getenv("THREADS_API_SECRET", "")

    # Email
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = 587
    SMTP_HOST: Optional[str] = os.getenv("SMTP_HOST", "")
    SMTP_USER: Optional[str] = os.getenv("SMTP_USER", "")
    SMTP_PASSWORD: Optional[str] = os.getenv("SMTP_PASSWORD", "")
    EMAILS_FROM_EMAIL: Optional[str] = os.getenv("EMAILS_FROM_EMAIL", "")
    EMAILS_FROM_NAME: Optional[str] = os.getenv("EMAILS_FROM_NAME", "")

    # SendGrid
    SENDGRID_API_KEY: str = os.getenv("SENDGRID_API_KEY", "")

    # Frontend URL (for email links)
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")

    # Web Push Notifications
    VAPID_PRIVATE_KEY: str = os.getenv("VAPID_PRIVATE_KEY", "")
    VAPID_PUBLIC_KEY: str = os.getenv("VAPID_PUBLIC_KEY", "")
    VAPID_CLAIMS_EMAIL: str = os.getenv("VAPID_CLAIMS_EMAIL", "")

    # Subscription Plans - Updated to match ACE Social 3-tier structure
    # Legacy plan IDs (deprecated - use new plan IDs below)
    FREE_PLAN_ID: str = "creator"  # Updated to match frontend
    BASIC_PLAN_ID: str = os.getenv("BASIC_PLAN_ID", "accelerator")  # Updated to match frontend
    PRO_PLAN_ID: str = os.getenv("PRO_PLAN_ID", "dominator")  # Updated to match frontend
    ENTERPRISE_PLAN_ID: str = os.getenv("ENTERPRISE_PLAN_ID", "dominator")  # Map to dominator

    # ACE Social 3-tier subscription plan structure
    CREATOR_PLAN_ID: str = "creator"      # Basic tier - 50 posts/month, 3 platforms
    ACCELERATOR_PLAN_ID: str = "accelerator"  # Growth tier - 200 posts/month, 6 platforms
    DOMINATOR_PLAN_ID: str = "dominator"  # Premium tier - Unlimited posts, all features

    # URL Shortener
    URL_SHORTENER_DOMAIN: str = os.getenv("URL_SHORTENER_DOMAIN", "https://b2b.link")
    IPINFO_API_KEY: str = os.getenv("IPINFO_API_KEY", "")

    # Redis Configuration (for distributed WebSockets)
    REDIS_ENABLED: bool = os.getenv("REDIS_ENABLED", "false").lower() == "true"  # Disabled by default for development
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD", None)
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_MAX_CONNECTIONS: int = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
    REDIS_SOCKET_TIMEOUT: int = int(os.getenv("REDIS_SOCKET_TIMEOUT", "5"))
    REDIS_CONNECTION_TIMEOUT: int = int(os.getenv("REDIS_CONNECTION_TIMEOUT", "5"))
    REDIS_RETRY_ON_TIMEOUT: bool = os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true"
    REDIS_HEALTH_CHECK_INTERVAL: int = int(os.getenv("REDIS_HEALTH_CHECK_INTERVAL", "30"))

    # Caching Configuration
    CACHE_ENABLED: bool = os.getenv("CACHE_ENABLED", "true").lower() == "true"
    CACHE_BACKEND: str = os.getenv("CACHE_BACKEND", "redis")  # redis, memory, none
    CACHE_DEFAULT_TTL: int = int(os.getenv("CACHE_DEFAULT_TTL", "300"))  # 5 minutes
    CACHE_MAX_MEMORY_ITEMS: int = int(os.getenv("CACHE_MAX_MEMORY_ITEMS", "10000"))
    CACHE_REDIS_URL: str = os.getenv("CACHE_REDIS_URL", REDIS_URL)  # Default to same Redis instance

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
    RATE_LIMIT_DEFAULT_LIMIT: int = int(os.getenv("RATE_LIMIT_DEFAULT_LIMIT", "100"))
    RATE_LIMIT_DEFAULT_PERIOD: int = int(os.getenv("RATE_LIMIT_DEFAULT_PERIOD", "3600"))  # 1 hour in seconds

    # Observability
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    ENABLE_METRICS: bool = os.getenv("ENABLE_METRICS", "false").lower() == "true"
    METRICS_ENDPOINT: Optional[str] = os.getenv("METRICS_ENDPOINT", None)

    # Instance identification (for distributed systems)
    INSTANCE_ID: str = os.getenv("INSTANCE_ID", f"instance-{os.getpid()}")

    # File uploads
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "uploads")
    MAX_UPLOAD_SIZE: int = int(os.getenv("MAX_UPLOAD_SIZE", "104857600"))  # 100MB in bytes

    # Avatar settings
    STORE_ORIGINAL_AVATARS: bool = os.getenv("STORE_ORIGINAL_AVATARS", "true").lower() == "true"
    AVATAR_QUALITY: int = int(os.getenv("AVATAR_QUALITY", "90"))  # JPEG quality (1-100)
    AVATAR_MAX_SIZE: int = int(os.getenv("AVATAR_MAX_SIZE", "5242880"))  # 5MB in bytes

    # Image Metadata Removal settings
    METADATA_REMOVAL_ENABLED: bool = os.getenv("METADATA_REMOVAL_ENABLED", "true").lower() == "true"
    METADATA_REMOVAL_PRESERVE_QUALITY: bool = os.getenv("METADATA_REMOVAL_PRESERVE_QUALITY", "true").lower() == "true"
    METADATA_REMOVAL_MAX_FILE_SIZE_MB: int = int(os.getenv("METADATA_REMOVAL_MAX_FILE_SIZE_MB", "50"))
    METADATA_REMOVAL_CACHE_TTL: int = int(os.getenv("METADATA_REMOVAL_CACHE_TTL", "3600"))  # 1 hour
    METADATA_REMOVAL_PERFORMANCE_TARGET_MS: int = int(os.getenv("METADATA_REMOVAL_PERFORMANCE_TARGET_MS", "200"))
    METADATA_REMOVAL_BATCH_SIZE_LIMIT: int = int(os.getenv("METADATA_REMOVAL_BATCH_SIZE_LIMIT", "10"))
    METADATA_REMOVAL_TRACK_USAGE: bool = os.getenv("METADATA_REMOVAL_TRACK_USAGE", "true").lower() == "true"

    # Job Queue Configuration
    JOB_WORKER_COUNT: int = int(os.getenv("JOB_WORKER_COUNT", "2"))
    JOB_POLL_INTERVAL: float = float(os.getenv("JOB_POLL_INTERVAL", "1.0"))
    JOB_TIMEOUT: int = int(os.getenv("JOB_TIMEOUT", "300"))  # 5 minutes

    # Whitelabel Authentication Configuration
    WHITELABEL_ENABLED: bool = os.getenv("WHITELABEL_ENABLED", "true").lower() == "true"
    WHITELABEL_ADMIN_EMAIL: str = os.getenv("WHITELABEL_ADMIN_EMAIL", "<EMAIL>")
    WHITELABEL_ADMIN_PASSWORD: str = os.getenv("WHITELABEL_ADMIN_PASSWORD", "Admin@1155")
    WHITELABEL_ADMIN_NAME: str = os.getenv("WHITELABEL_ADMIN_NAME", "Admin User")
    WHITELABEL_COMPANY_NAME: str = os.getenv("WHITELABEL_COMPANY_NAME", "ACEO")

    # Whitelabel Branding Configuration
    WHITELABEL_BRAND_NAME: str = os.getenv("WHITELABEL_BRAND_NAME", "ACE Social")
    WHITELABEL_BRAND_LOGO: str = os.getenv("WHITELABEL_BRAND_LOGO", "")
    WHITELABEL_PRIMARY_COLOR: str = os.getenv("WHITELABEL_PRIMARY_COLOR", "#4E40C5")
    WHITELABEL_SECONDARY_COLOR: str = os.getenv("WHITELABEL_SECONDARY_COLOR", "#EBAE1B")
    WHITELABEL_ACCENT_COLOR: str = os.getenv("WHITELABEL_ACCENT_COLOR", "#EBAE1B")
    WHITELABEL_BACKGROUND_COLOR: str = os.getenv("WHITELABEL_BACKGROUND_COLOR", "#FFFFFF")
    WHITELABEL_TEXT_COLOR: str = os.getenv("WHITELABEL_TEXT_COLOR", "#15110E")

    # Localhost Development Configuration
    LOCALHOST_ONLY: bool = os.getenv("LOCALHOST_ONLY", "true").lower() == "true"
    LOCALHOST_BYPASS_AUTH: bool = os.getenv("LOCALHOST_BYPASS_AUTH", "true").lower() == "true"
    LOCALHOST_BYPASS_ADMIN_AUTH: bool = os.getenv("LOCALHOST_BYPASS_ADMIN_AUTH", "true").lower() == "true"
    ALLOWED_LOCALHOST_PORTS: List[int] = [3000, 3001, 5173, 8000, 8080]

    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=str(env_path)
    )


settings = Settings()
