/**
 * Whitelabel Login Component
 * Provides a customizable login interface with branding support
 */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  IconButton,
  InputAdornment,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Login as LoginIcon,
  DeveloperMode,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import useWhitelabel from '../../hooks/useWhitelabel';
import Logo from '../common/Logo';

const WhitelabelLogin = ({ onSuccess, onError }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { login } = useAuth();
  const {
    isEnabled,
    isDevelopment,
    brandName,
    logoUrl,
    colors,
    getDevCredentials,
    getBrandingCSSVariables,
    status,
  } = useWhitelabel();

  // Auto-fill development credentials if available
  useEffect(() => {
    if (isDevelopment && isEnabled) {
      const devCreds = getDevCredentials();
      if (devCreds) {
        setEmail(devCreds.email);
        setPassword(devCreds.password);
      }
    }
  }, [isDevelopment, isEnabled, getDevCredentials]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await login(email, password);
      
      if (result.success) {
        onSuccess?.(result);
      } else {
        setError(result.error || 'Login failed');
        onError?.(result.error);
      }
    } catch (err) {
      const errorMessage = err.message || 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickLogin = () => {
    const devCreds = getDevCredentials();
    if (devCreds) {
      setEmail(devCreds.email);
      setPassword(devCreds.password);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Apply branding styles
  const brandingStyles = getBrandingCSSVariables();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${colors.primary}20, ${colors.secondary}20)`,
        padding: 2,
        ...brandingStyles,
      }}
    >
      <Card
        sx={{
          maxWidth: 400,
          width: '100%',
          boxShadow: 3,
          borderRadius: 2,
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Logo and Brand Name */}
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Box sx={{ mb: 2 }}>
              {logoUrl ? (
                // Use custom whitelabel logo if provided
                <img
                  src={logoUrl}
                  alt={`${brandName} Logo`}
                  style={{
                    maxHeight: 60,
                    maxWidth: '100%',
                    objectFit: 'contain',
                  }}
                />
              ) : (
                // Use default ACE Social logo
                <Logo
                  variant="main"
                  size="medium"
                  maxHeight={60}
                  alt="ACE Social Logo"
                />
              )}
            </Box>
            <Typography
              variant="h4"
              component="h1"
              sx={{
                fontWeight: 'bold',
                color: colors.primary,
                mb: 1,
              }}
            >
              {brandName}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ mb: 2 }}
            >
              Sign in to your account
            </Typography>
          </Box>

          {/* Development Mode Indicator */}
          {isDevelopment && isEnabled && (
            <Box sx={{ mb: 2 }}>
              <Chip
                icon={<DeveloperMode />}
                label="Development Mode"
                color="info"
                variant="outlined"
                size="small"
                sx={{ mb: 1 }}
              />
              {status?.admin_user_exists && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={handleQuickLogin}
                  sx={{ ml: 1, fontSize: '0.75rem' }}
                >
                  Use Dev Credentials
                </Button>
              )}
            </Box>
          )}

          {/* Error Alert */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {/* Login Form */}
          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
              sx={{ mb: 2 }}
              autoComplete="email"
            />

            <TextField
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              sx={{ mb: 3 }}
              autoComplete="current-password"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={togglePasswordVisibility}
                      edge="end"
                      disabled={loading}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading || !email || !password}
              startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}
              sx={{
                py: 1.5,
                backgroundColor: colors.primary,
                '&:hover': {
                  backgroundColor: colors.primary + 'dd',
                },
              }}
            >
              {loading ? 'Signing In...' : 'Sign In'}
            </Button>
          </Box>

          {/* Development Info */}
          {isDevelopment && isEnabled && (
            <>
              <Divider sx={{ my: 3 }} />
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Development Environment
                </Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Default Admin: {status?.admin_email}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Whitelabel authentication enabled for localhost
                </Typography>
              </Box>
            </>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default WhitelabelLogin;
